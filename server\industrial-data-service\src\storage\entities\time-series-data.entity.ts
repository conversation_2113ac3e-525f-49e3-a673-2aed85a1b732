import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { Device } from '../../device-management/entities/device.entity';

export enum DataQuality {
  GOOD = 'good',
  BAD = 'bad',
  UNCERTAIN = 'uncertain',
  STALE = 'stale'
}

@Entity('time_series_data')
@Index(['deviceId', 'tagName', 'timestamp'])
@Index(['timestamp'])
@Index(['deviceId', 'timestamp'])
export class TimeSeriesData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  deviceId: string;

  @Column({ length: 100 })
  tagName: string;

  @Column({ type: 'text', nullable: true })
  tagDescription: string;

  @Column({ type: 'json' })
  value: any;

  @Column({ type: 'varchar', length: 50, nullable: true })
  unit: string;

  @Column({
    type: 'enum',
    enum: DataQuality,
    default: DataQuality.GOOD
  })
  quality: DataQuality;

  @Column({ type: 'timestamp', precision: 3 })
  timestamp: Date;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Device, device => device.dataPoints)
  @JoinColumn({ name: 'deviceId' })
  device: Device;
}
