import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { TimeSeriesData, DataQuality } from './entities/time-series-data.entity';
import { DataArchive, ArchiveStatus, CompressionType } from './entities/data-archive.entity';
import { StoreDataDto, BatchStoreDataDto, StoreDataPointDto } from './dto/store-data.dto';
import { QueryDataDto, AggregationType } from './dto/query-data.dto';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);

  constructor(
    @InjectRepository(TimeSeriesData)
    private timeSeriesRepository: Repository<TimeSeriesData>,
    @InjectRepository(DataArchive)
    private archiveRepository: Repository<DataArchive>,
  ) {}

  /**
   * 存储单个设备的数据点
   */
  async storeData(storeDataDto: StoreDataDto): Promise<TimeSeriesData[]> {
    try {
      const { deviceId, dataPoints } = storeDataDto;
      const timeSeriesData: TimeSeriesData[] = [];

      for (const dataPoint of dataPoints) {
        const data = this.timeSeriesRepository.create({
          deviceId,
          tagName: dataPoint.tagName,
          tagDescription: dataPoint.tagDescription,
          value: dataPoint.value,
          unit: dataPoint.unit,
          quality: dataPoint.quality || DataQuality.GOOD,
          timestamp: dataPoint.timestamp ? new Date(dataPoint.timestamp) : new Date(),
          metadata: dataPoint.metadata
        });

        timeSeriesData.push(data);
      }

      const savedData = await this.timeSeriesRepository.save(timeSeriesData);
      this.logger.debug(`数据存储成功: 设备 ${deviceId}, ${dataPoints.length} 个数据点`);
      
      return savedData;

    } catch (error) {
      this.logger.error(`数据存储失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量存储多个设备的数据
   */
  async batchStoreData(batchStoreDataDto: BatchStoreDataDto): Promise<{ success: number; failed: number; results: any[] }> {
    const results = [];
    let successCount = 0;
    let failedCount = 0;

    for (const deviceData of batchStoreDataDto.devices) {
      try {
        const savedData = await this.storeData(deviceData);
        results.push({
          deviceId: deviceData.deviceId,
          success: true,
          count: savedData.length
        });
        successCount++;
      } catch (error) {
        results.push({
          deviceId: deviceData.deviceId,
          success: false,
          error: error.message
        });
        failedCount++;
      }
    }

    this.logger.log(`批量数据存储完成: 成功 ${successCount}, 失败 ${failedCount}`);

    return {
      success: successCount,
      failed: failedCount,
      results
    };
  }

  /**
   * 查询时序数据
   */
  async queryData(queryDto: QueryDataDto): Promise<{ data: any[]; total: number; aggregated: boolean }> {
    try {
      const {
        deviceId,
        deviceIds,
        tagName,
        tagNames,
        startTime,
        endTime,
        quality,
        aggregation,
        interval,
        limit,
        offset,
        sortOrder
      } = queryDto;

      const queryBuilder = this.timeSeriesRepository.createQueryBuilder('data');

      // 设备过滤
      if (deviceId) {
        queryBuilder.andWhere('data.deviceId = :deviceId', { deviceId });
      } else if (deviceIds && deviceIds.length > 0) {
        queryBuilder.andWhere('data.deviceId IN (:...deviceIds)', { deviceIds });
      }

      // 标签过滤
      if (tagName) {
        queryBuilder.andWhere('data.tagName = :tagName', { tagName });
      } else if (tagNames && tagNames.length > 0) {
        queryBuilder.andWhere('data.tagName IN (:...tagNames)', { tagNames });
      }

      // 时间范围过滤
      queryBuilder.andWhere('data.timestamp BETWEEN :startTime AND :endTime', {
        startTime: new Date(startTime),
        endTime: new Date(endTime)
      });

      // 质量过滤
      if (quality) {
        queryBuilder.andWhere('data.quality = :quality', { quality });
      }

      // 聚合查询
      if (aggregation && aggregation !== AggregationType.NONE && interval) {
        return await this.executeAggregatedQuery(queryBuilder, aggregation, interval, sortOrder);
      }

      // 普通查询
      queryBuilder
        .orderBy('data.timestamp', sortOrder)
        .skip(offset)
        .take(limit);

      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        aggregated: false
      };

    } catch (error) {
      this.logger.error(`数据查询失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 执行聚合查询
   */
  private async executeAggregatedQuery(
    queryBuilder: any,
    aggregation: AggregationType,
    interval: number,
    sortOrder: 'ASC' | 'DESC'
  ): Promise<{ data: any[]; total: number; aggregated: boolean }> {
    
    // 构建聚合查询
    const aggregateFunction = this.getAggregateFunction(aggregation);
    
    queryBuilder
      .select([
        'data.deviceId',
        'data.tagName',
        `${aggregateFunction}(data.value) as value`,
        'data.unit',
        `DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(data.timestamp) / ${interval}) * ${interval}), '%Y-%m-%d %H:%i:%s') as timestamp`
      ])
      .groupBy('data.deviceId, data.tagName, FLOOR(UNIX_TIMESTAMP(data.timestamp) / :interval)')
      .setParameter('interval', interval)
      .orderBy('timestamp', sortOrder);

    const data = await queryBuilder.getRawMany();

    return {
      data,
      total: data.length,
      aggregated: true
    };
  }

  /**
   * 获取聚合函数
   */
  private getAggregateFunction(aggregation: AggregationType): string {
    switch (aggregation) {
      case AggregationType.AVG:
        return 'AVG';
      case AggregationType.MIN:
        return 'MIN';
      case AggregationType.MAX:
        return 'MAX';
      case AggregationType.SUM:
        return 'SUM';
      case AggregationType.COUNT:
        return 'COUNT';
      case AggregationType.FIRST:
        return 'FIRST_VALUE';
      case AggregationType.LAST:
        return 'LAST_VALUE';
      default:
        return 'AVG';
    }
  }

  /**
   * 获取最新数据
   */
  async getLatestData(deviceId: string, tagName?: string): Promise<TimeSeriesData[]> {
    try {
      const queryBuilder = this.timeSeriesRepository.createQueryBuilder('data')
        .where('data.deviceId = :deviceId', { deviceId });

      if (tagName) {
        queryBuilder.andWhere('data.tagName = :tagName', { tagName });
      }

      const data = await queryBuilder
        .orderBy('data.timestamp', 'DESC')
        .limit(tagName ? 1 : 100)
        .getMany();

      return data;

    } catch (error) {
      this.logger.error(`获取最新数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除过期数据
   */
  async deleteExpiredData(retentionDays: number): Promise<{ deletedCount: number }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result = await this.timeSeriesRepository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoffDate', { cutoffDate })
        .execute();

      this.logger.log(`删除过期数据: ${result.affected} 条记录`);

      return { deletedCount: result.affected || 0 };

    } catch (error) {
      this.logger.error(`删除过期数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStatistics(): Promise<any> {
    try {
      const totalRecords = await this.timeSeriesRepository.count();
      
      const deviceStats = await this.timeSeriesRepository
        .createQueryBuilder('data')
        .select('data.deviceId', 'deviceId')
        .addSelect('COUNT(*)', 'count')
        .groupBy('data.deviceId')
        .getRawMany();

      const tagStats = await this.timeSeriesRepository
        .createQueryBuilder('data')
        .select('data.tagName', 'tagName')
        .addSelect('COUNT(*)', 'count')
        .groupBy('data.tagName')
        .orderBy('count', 'DESC')
        .limit(10)
        .getRawMany();

      const qualityStats = await this.timeSeriesRepository
        .createQueryBuilder('data')
        .select('data.quality', 'quality')
        .addSelect('COUNT(*)', 'count')
        .groupBy('data.quality')
        .getRawMany();

      return {
        totalRecords,
        deviceCount: deviceStats.length,
        deviceStats,
        topTags: tagStats,
        qualityStats
      };

    } catch (error) {
      this.logger.error(`获取存储统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 定时清理过期数据（每天凌晨2点执行）
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async scheduledDataCleanup(): Promise<void> {
    try {
      const retentionDays = parseInt(process.env.DATA_RETENTION_DAYS || '365');
      const result = await this.deleteExpiredData(retentionDays);
      
      this.logger.log(`定时数据清理完成: 删除 ${result.deletedCount} 条过期记录`);
    } catch (error) {
      this.logger.error(`定时数据清理失败: ${error.message}`, error.stack);
    }
  }
}
